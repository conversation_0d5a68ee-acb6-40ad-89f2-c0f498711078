package com.example.audit.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - gateway-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 06/30/2021 18:14
 **********************************************************************************************************************/

@Data
@Builder
public class LoginUserInfo implements Serializable {

    private Long userId;

    /**
     * 对应LoginSourceEnum值
     */
    private String fromType;

    private String cId;

    private String nickName;

    private String phoneNumber;

    private String roles;

    private String scopes;

    private String companyIds;

    private String projectIds;

    private String buildingIds;

    private String brandId;

    private String lbsId;

    private String source;

    private String openId;

    private Long brandUserId;

    private String signupMethod;

}
