<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::content}, ~{::scripts})}">
<head>
    <title>Audit Trail - JPA Auditing Demo</title>
</head>
<body>
    <div th:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-history me-2"></i>Audit Trail
                </h1>
                <p class="text-muted mb-0">
                    <span th:text="${entityName}">Entity</span> ID: 
                    <code th:text="${entityId}">123</code>
                </p>
            </div>
            <div>
                <button class="btn btn-outline-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left me-1"></i>Back
                </button>
                <button class="btn btn-outline-primary" onclick="refreshAuditTrail()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>

        <!-- Entity Information -->
        <div th:if="${entity}" class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Entity Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row" th:if="${entity.class.simpleName == 'User'}">
                    <div class="col-md-6">
                        <strong>Name:</strong> <span th:text="${entity.fullName}">John Doe</span><br>
                        <strong>Username:</strong> <span th:text="${entity.username}">johndoe</span><br>
                        <strong>Email:</strong> <span th:text="${entity.email}"><EMAIL></span>
                    </div>
                    <div class="col-md-6">
                        <strong>Department:</strong> <span th:text="${entity.department ?: 'N/A'}">IT</span><br>
                        <strong>Status:</strong> 
                        <span class="badge" 
                              th:classappend="${entity.status.name() == 'ACTIVE'} ? 'bg-success' : 'bg-secondary'"
                              th:text="${entity.status}">ACTIVE</span><br>
                        <strong>Created:</strong> <span th:text="${#temporals.format(entity.createdAt, 'MMM dd, yyyy HH:mm')}">Jan 01, 2024</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>Filters
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Operation</label>
                        <select id="operationFilter" class="form-select form-select-sm">
                            <option value="">All Operations</option>
                            <option value="CREATE">Create</option>
                            <option value="UPDATE">Update</option>
                            <option value="DELETE">Delete</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">User</label>
                        <input type="text" id="userFilter" class="form-control form-control-sm" placeholder="Filter by user">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date From</label>
                        <input type="datetime-local" id="dateFromFilter" class="form-control form-control-sm">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date To</label>
                        <input type="datetime-local" id="dateToFilter" class="form-control form-control-sm">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <button class="btn btn-primary btn-sm" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>Apply Filters
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Trail -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Change History
                    <span id="auditCount" class="badge bg-secondary ms-2">0</span>
                </h5>
            </div>
            <div class="card-body">
                <!-- Loading indicator -->
                <div id="loadingIndicator" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading audit trail...</p>
                </div>

                <!-- No data message -->
                <div id="noDataMessage" class="text-center py-4 d-none">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No audit records found for this entity.</p>
                </div>

                <!-- Audit timeline -->
                <div id="auditTimeline" class="audit-timeline d-none">
                    <!-- Timeline items will be populated by JavaScript -->
                </div>

                <!-- Pagination -->
                <nav id="paginationNav" class="d-none">
                    <ul class="pagination justify-content-center">
                        <!-- Pagination will be populated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <div th:fragment="scripts">
        <script th:inline="javascript">
            const entityName = /*[[${entityName}]]*/ 'Entity';
            const entityId = /*[[${entityId}]]*/ '1';
            
            let currentPage = 0;
            let pageSize = 20;
            let currentFilters = {};

            // Load audit trail on page load
            document.addEventListener('DOMContentLoaded', function() {
                loadAuditTrail();
            });

            function loadAuditTrail(page = 0) {
                currentPage = page;
                
                // Show loading indicator
                document.getElementById('loadingIndicator').classList.remove('d-none');
                document.getElementById('auditTimeline').classList.add('d-none');
                document.getElementById('noDataMessage').classList.add('d-none');
                document.getElementById('paginationNav').classList.add('d-none');

                // Build URL with filters
                let url = `/api/audit/entity/${entityName}/${entityId}?page=${page}&size=${pageSize}`;
                
                // Add filters to URL
                Object.keys(currentFilters).forEach(key => {
                    if (currentFilters[key]) {
                        url += `&${key}=${encodeURIComponent(currentFilters[key])}`;
                    }
                });

                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        displayAuditTrail(data);
                    })
                    .catch(error => {
                        console.error('Error loading audit trail:', error);
                        document.getElementById('loadingIndicator').classList.add('d-none');
                        document.getElementById('noDataMessage').classList.remove('d-none');
                    });
            }

            function displayAuditTrail(data) {
                document.getElementById('loadingIndicator').classList.add('d-none');
                
                const timeline = document.getElementById('auditTimeline');
                const auditCount = document.getElementById('auditCount');
                
                auditCount.textContent = data.totalElements;

                if (data.content.length === 0) {
                    document.getElementById('noDataMessage').classList.remove('d-none');
                    return;
                }

                // Clear existing timeline
                timeline.innerHTML = '';
                
                // Add timeline items
                data.content.forEach(auditLog => {
                    const timelineItem = createTimelineItem(auditLog);
                    timeline.appendChild(timelineItem);
                });

                timeline.classList.remove('d-none');
                
                // Update pagination
                updatePagination(data);
            }

            function createTimelineItem(auditLog) {
                const item = document.createElement('div');
                item.className = `audit-timeline-item ${auditLog.operation.toLowerCase()}`;
                
                const timestamp = new Date(auditLog.timestamp).toLocaleString();
                const operationBadge = getOperationBadge(auditLog.operation);
                
                item.innerHTML = `
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                ${operationBadge}
                                <strong>${auditLog.operation}</strong>
                                <span class="text-muted">by ${auditLog.userNickname || 'System'}</span>
                            </div>
                            <small class="text-muted">${timestamp}</small>
                        </div>
                        <div class="card-body">
                            ${auditLog.reason ? `<p class="mb-2"><strong>Reason:</strong> ${auditLog.reason}</p>` : ''}
                            <div class="field-changes">
                                ${createFieldChanges(auditLog.details)}
                            </div>
                        </div>
                    </div>
                `;
                
                return item;
            }

            function createFieldChanges(details) {
                if (!details || details.length === 0) {
                    return '<p class="text-muted">No field changes recorded.</p>';
                }
                
                return details.map(detail => {
                    const sensitiveClass = detail.sensitive ? ' sensitive' : '';
                    const changeDescription = getChangeDescription(detail);
                    
                    return `
                        <div class="field-change${sensitiveClass}">
                            <strong>${detail.fieldName}</strong> 
                            <span class="badge bg-light text-dark">${detail.fieldType}</span>
                            ${detail.sensitive ? '<span class="badge bg-danger ms-1">Sensitive</span>' : ''}
                            <br>
                            <small>${changeDescription}</small>
                        </div>
                    `;
                }).join('');
            }

            function getChangeDescription(detail) {
                if (detail.oldValue === null && detail.newValue !== null) {
                    return `Set to: <code>${detail.newValue}</code>`;
                } else if (detail.oldValue !== null && detail.newValue === null) {
                    return `Removed (was: <code>${detail.oldValue}</code>)`;
                } else if (detail.oldValue !== null && detail.newValue !== null) {
                    return `Changed from <code>${detail.oldValue}</code> to <code>${detail.newValue}</code>`;
                } else {
                    return 'Field accessed';
                }
            }

            function getOperationBadge(operation) {
                const badgeClass = AuditDemo.getOperationBadgeClass(operation);
                return `<span class="badge ${badgeClass} me-2">${operation}</span>`;
            }

            function updatePagination(data) {
                const nav = document.getElementById('paginationNav');
                const pagination = nav.querySelector('.pagination');
                
                if (data.totalPages <= 1) {
                    nav.classList.add('d-none');
                    return;
                }
                
                pagination.innerHTML = '';
                
                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = `page-item ${data.first ? 'disabled' : ''}`;
                prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadAuditTrail(${data.number - 1})">Previous</a>`;
                pagination.appendChild(prevLi);
                
                // Page numbers
                for (let i = 0; i < data.totalPages; i++) {
                    const li = document.createElement('li');
                    li.className = `page-item ${i === data.number ? 'active' : ''}`;
                    li.innerHTML = `<a class="page-link" href="#" onclick="loadAuditTrail(${i})">${i + 1}</a>`;
                    pagination.appendChild(li);
                }
                
                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = `page-item ${data.last ? 'disabled' : ''}`;
                nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadAuditTrail(${data.number + 1})">Next</a>`;
                pagination.appendChild(nextLi);
                
                nav.classList.remove('d-none');
            }

            function applyFilters() {
                currentFilters = {
                    operation: document.getElementById('operationFilter').value,
                    userNickname: document.getElementById('userFilter').value,
                    startDate: document.getElementById('dateFromFilter').value,
                    endDate: document.getElementById('dateToFilter').value
                };
                
                loadAuditTrail(0);
            }

            function clearFilters() {
                document.getElementById('operationFilter').value = '';
                document.getElementById('userFilter').value = '';
                document.getElementById('dateFromFilter').value = '';
                document.getElementById('dateToFilter').value = '';
                
                currentFilters = {};
                loadAuditTrail(0);
            }

            function refreshAuditTrail() {
                loadAuditTrail(currentPage);
            }
        </script>
    </div>
</body>
</html>
