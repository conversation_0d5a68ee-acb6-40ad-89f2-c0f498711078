<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::content}, ~{::scripts})}">
<head>
    <title>Users - JPA Auditing Demo</title>
</head>
<body>
    <div th:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-users me-2"></i>Users Management
                </h1>
                <p class="text-muted">Manage users and view their audit trails</p>
            </div>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                    <i class="fas fa-plus me-1"></i>Create User
                </button>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" action="/users">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="Search users..." th:value="${search}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="size" class="form-select" onchange="this.form.submit()">
                                <option value="10" th:selected="${pageSize == 10}">10 per page</option>
                                <option value="25" th:selected="${pageSize == 25}">25 per page</option>
                                <option value="50" th:selected="${pageSize == 50}">50 per page</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <a href="/users" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    Users 
                    <span class="badge bg-secondary ms-2" th:text="${users.totalElements}">0</span>
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${users.empty}" class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No users found.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <i class="fas fa-plus me-1"></i>Create First User
                    </button>
                </div>

                <div th:unless="${users.empty}" class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Department</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="user : ${users.content}">
                                <td>
                                    <div>
                                        <strong th:text="${user.fullName}">John Doe</strong>
                                        <br>
                                        <small class="text-muted" th:text="${user.username}">johndoe</small>
                                    </div>
                                </td>
                                <td th:text="${user.email}"><EMAIL></td>
                                <td>
                                    <span th:if="${user.department}" th:text="${user.department}">IT</span>
                                    <span th:unless="${user.department}" class="text-muted">-</span>
                                </td>
                                <td>
                                    <span class="badge" 
                                          th:classappend="${user.status.name() == 'ACTIVE'} ? 'bg-success' : 'bg-secondary'"
                                          th:text="${user.status}">ACTIVE</span>
                                </td>
                                <td>
                                    <small th:text="${#temporals.format(user.createdAt, 'MMM dd, yyyy')}">
                                        Jan 01, 2024
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a th:href="@{/users/{id}(id=${user.id})}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-secondary" 
                                                th:onclick="|editUser(${user.id})|" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" 
                                                th:onclick="|AuditDemo.showAuditTrail('User', '${user.id}')|" 
                                                title="Audit Trail">
                                            <i class="fas fa-history"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                th:onclick="|deleteUser(${user.id}, '${user.username}')|" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <nav th:if="${users.totalPages > 1}">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${users.first} ? 'disabled'">
                            <a class="page-link" th:href="@{/users(page=${users.number - 1}, size=${pageSize}, search=${search})}">
                                Previous
                            </a>
                        </li>
                        
                        <li th:each="pageNum : ${#numbers.sequence(0, users.totalPages - 1)}" 
                            class="page-item" th:classappend="${pageNum == users.number} ? 'active'">
                            <a class="page-link" 
                               th:href="@{/users(page=${pageNum}, size=${pageSize}, search=${search})}"
                               th:text="${pageNum + 1}">1</a>
                        </li>
                        
                        <li class="page-item" th:classappend="${users.last} ? 'disabled'">
                            <a class="page-link" th:href="@{/users(page=${users.number + 1}, size=${pageSize}, search=${search})}">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Create User Modal -->
        <div class="modal fade" id="createUserModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create New User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createUserForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">First Name *</label>
                                        <input type="text" name="firstName" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Last Name *</label>
                                        <input type="text" name="lastName" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Username *</label>
                                <input type="text" name="username" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email *</label>
                                <input type="email" name="email" class="form-control" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Department</label>
                                        <input type="text" name="department" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Job Title</label>
                                        <input type="text" name="jobTitle" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" name="phoneNumber" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="ACTIVE">Active</option>
                                    <option value="INACTIVE">Inactive</option>
                                    <option value="PENDING">Pending</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="createUser()">Create User</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div th:fragment="scripts">
        <script>
            function createUser() {
                const form = document.getElementById('createUserForm');
                const formData = new FormData(form);
                const userData = Object.fromEntries(formData.entries());
                
                fetch('/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-user': JSON.stringify({
                            userId: 1,
                            nickName: 'admin',
                            fromType: 'web'
                        })
                    },
                    body: JSON.stringify(userData)
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        throw new Error('Failed to create user');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to create user');
                });
            }
            
            function editUser(userId) {
                // Implement edit functionality
                alert('Edit functionality would be implemented here');
            }
            
            function deleteUser(userId, username) {
                if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                    fetch(`/api/users/${userId}`, {
                        method: 'DELETE',
                        headers: {
                            'x-user': JSON.stringify({
                                userId: 1,
                                nickName: 'admin',
                                fromType: 'web'
                            })
                        }
                    })
                    .then(response => {
                        if (response.ok) {
                            location.reload();
                        } else {
                            throw new Error('Failed to delete user');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to delete user');
                    });
                }
            }
        </script>
    </div>
</body>
</html>
