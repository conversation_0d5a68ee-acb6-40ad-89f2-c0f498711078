package com.example.audit;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Main application class for JPA Auditing Demo
 *
 * This application demonstrates a comprehensive, reusable data auditing system
 * with field-level change tracking, user context management, and temporal auditing.
 *
 * Features:
 * - Field-level change tracking for all database operations
 * - User context extraction from x-user HTTP header
 * - Temporal auditing with timezone support
 * - Generic framework for multi-entity support
 * - Enterprise-grade audit trail UI
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
public class JpaAuditingDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(JpaAuditingDemoApplication.class, args);
    }
}
