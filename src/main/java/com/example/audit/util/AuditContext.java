package com.example.audit.util;

import com.example.audit.dto.LoginUserInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * Thread-local context for storing audit information during request processing.
 * This class manages user context extracted from HTTP headers and provides
 * it to the audit system for tracking changes.
 * 
 * <AUTHOR> Agent
 */
@Slf4j
public class AuditContext {
    
    private static final ThreadLocal<LoginUserInfo> userContext = new ThreadLocal<>();
    private static final ThreadLocal<String> operationContext = new ThreadLocal<>();
    
    /**
     * Set the current user context for the thread
     * 
     * @param userInfo the user information extracted from request headers
     */
    public static void setCurrentUser(LoginUserInfo userInfo) {
        userContext.set(userInfo);
        log.debug("Set audit context for user: {}", 
                 userInfo != null ? userInfo.getNickName() : "anonymous");
    }
    
    /**
     * Get the current user context for the thread
     * 
     * @return the current user information, or null if not set
     */
    public static LoginUserInfo getCurrentUser() {
        return userContext.get();
    }
    
    /**
     * Get the current user's nickname for audit logging
     * 
     * @return the nickname of the current user, or "system" if not available
     */
    public static String getCurrentUserNickname() {
        LoginUserInfo user = getCurrentUser();
        return user != null && user.getNickName() != null ? 
               user.getNickName() : "system";
    }
    
    /**
     * Get the current user's ID for audit logging
     * 
     * @return the ID of the current user, or null if not available
     */
    public static Long getCurrentUserId() {
        LoginUserInfo user = getCurrentUser();
        return user != null ? user.getUserId() : null;
    }
    
    /**
     * Set additional operation context information
     * 
     * @param operation the operation being performed
     */
    public static void setOperation(String operation) {
        operationContext.set(operation);
    }
    
    /**
     * Get the current operation context
     * 
     * @return the current operation, or null if not set
     */
    public static String getCurrentOperation() {
        return operationContext.get();
    }
    
    /**
     * Clear all context information for the current thread.
     * This should be called at the end of request processing to prevent memory leaks.
     */
    public static void clear() {
        userContext.remove();
        operationContext.remove();
        log.debug("Cleared audit context for current thread");
    }
    
    /**
     * Check if user context is available
     * 
     * @return true if user context is set, false otherwise
     */
    public static boolean hasUserContext() {
        return getCurrentUser() != null;
    }
}
