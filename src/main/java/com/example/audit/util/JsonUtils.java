package com.example.audit.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for JSON serialization and deserialization operations.
 * Used by the audit system for converting objects to JSON strings
 * and parsing JSON strings back to objects.
 * 
 * <AUTHOR> Agent
 */
@Slf4j
public class JsonUtils {
    
    private static final ObjectMapper objectMapper;
    
    static {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }
    
    /**
     * Convert an object to JSON string
     * 
     * @param object the object to convert
     * @return JSON string representation, or null if conversion fails
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to JSON: {}", e.getMessage(), e);
            return object.toString();
        }
    }
    
    /**
     * Convert an object to pretty-printed JSON string
     * 
     * @param object the object to convert
     * @return pretty-printed JSON string, or null if conversion fails
     */
    public static String toPrettyJson(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to pretty JSON: {}", e.getMessage(), e);
            return object.toString();
        }
    }
    
    /**
     * Parse JSON string to object of specified type
     * 
     * @param json the JSON string to parse
     * @param clazz the target class type
     * @param <T> the type parameter
     * @return parsed object, or null if parsing fails
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON to {}: {}", clazz.getSimpleName(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Convert a value to a safe string representation for audit logging.
     * Handles null values and limits string length to prevent excessive storage.
     * 
     * @param value the value to convert
     * @param maxLength maximum length of the resulting string
     * @return safe string representation
     */
    public static String toSafeString(Object value, int maxLength) {
        if (value == null) {
            return null;
        }
        
        String stringValue;
        if (value instanceof String) {
            stringValue = (String) value;
        } else {
            stringValue = toJson(value);
            if (stringValue == null) {
                stringValue = value.toString();
            }
        }
        
        // Truncate if too long
        if (stringValue.length() > maxLength) {
            stringValue = stringValue.substring(0, maxLength - 3) + "...";
        }
        
        return stringValue;
    }
    
    /**
     * Get the ObjectMapper instance for advanced operations
     * 
     * @return the configured ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
}
