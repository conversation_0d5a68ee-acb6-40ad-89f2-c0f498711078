package com.example.audit.service;

import com.example.audit.dto.LoginUserInfo;
import com.example.audit.entity.AuditDetail;
import com.example.audit.entity.AuditLog;
import com.example.audit.entity.AuditableEntity;
import com.example.audit.repository.AuditLogRepository;
import com.example.audit.util.AuditContext;
import com.example.audit.util.JsonUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Core audit service responsible for capturing and persisting entity changes.
 *
 * This service provides field-level change tracking by comparing entity states
 * and creating detailed audit records. It supports asynchronous processing
 * to minimize impact on application performance.
 *
 * <AUTHOR> Agent
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

    private final AuditLogRepository auditLogRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Value("${audit.enabled:true}")
    private boolean auditEnabled;

    @Value("${audit.async:true}")
    private boolean asyncAudit;

    @Value("${audit.max-field-length:1000}")
    private int maxFieldLength;

    @Value("#{'${audit.excluded-fields:password,token,secret}'.split(',')}")
    private Set<String> excludedFields;

    /**
     * Audit entity creation
     *
     * @param entity the created entity
     */
    public void auditCreate(Object entity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditCreateAsync(entity);
        } else {
            doAuditCreate(entity);
        }
    }

    /**
     * Audit entity update
     *
     * @param entity the updated entity
     */
    public void auditUpdate(Object entity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditUpdateAsync(entity);
        } else {
            doAuditUpdate(entity);
        }
    }

    /**
     * Audit entity deletion
     *
     * @param entity the deleted entity
     */
    public void auditDelete(Object entity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditDeleteAsync(entity);
        } else {
            doAuditDelete(entity);
        }
    }

    @Async
    public void auditCreateAsync(Object entity) {
        doAuditCreate(entity);
    }

    @Async
    public void auditUpdateAsync(Object entity) {
        doAuditUpdate(entity);
    }

    @Async
    public void auditDeleteAsync(Object entity) {
        doAuditDelete(entity);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditCreate(Object entity) {
        try {
            AuditLog auditLog = createAuditLog(entity, AuditLog.AuditOperation.CREATE);

            // For CREATE operations, all non-null fields are considered "new"
            List<AuditDetail> details = extractFieldDetails(entity, null, entity);
            details.forEach(auditLog::addDetail);

            auditLogRepository.save(auditLog);
            log.debug("Audited CREATE operation for entity: {} with ID: {}",
                     auditLog.getEntityName(), auditLog.getEntityId());

        } catch (Exception e) {
            log.error("Failed to audit CREATE operation: {}", e.getMessage(), e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditUpdate(Object entity) {
        try {
            // Get the original entity state from database
            Object originalEntity = getOriginalEntity(entity);

            AuditLog auditLog = createAuditLog(entity, AuditLog.AuditOperation.UPDATE);

            // Compare original and current states
            List<AuditDetail> details = extractFieldDetails(entity, originalEntity, entity);

            // Only save if there are actual changes
            if (!details.isEmpty()) {
                details.forEach(auditLog::addDetail);
                auditLogRepository.save(auditLog);
                log.debug("Audited UPDATE operation for entity: {} with ID: {} ({} changes)",
                         auditLog.getEntityName(), auditLog.getEntityId(), details.size());
            }

        } catch (Exception e) {
            log.error("Failed to audit UPDATE operation: {}", e.getMessage(), e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditDelete(Object entity) {
        try {
            AuditLog auditLog = createAuditLog(entity, AuditLog.AuditOperation.DELETE);

            // For DELETE operations, all current fields are considered "removed"
            List<AuditDetail> details = extractFieldDetails(entity, entity, null);
            details.forEach(auditLog::addDetail);

            auditLogRepository.save(auditLog);
            log.debug("Audited DELETE operation for entity: {} with ID: {}",
                     auditLog.getEntityName(), auditLog.getEntityId());

        } catch (Exception e) {
            log.error("Failed to audit DELETE operation: {}", e.getMessage(), e);
        }
    }

    private AuditLog createAuditLog(Object entity, AuditLog.AuditOperation operation) {
        LoginUserInfo currentUser = AuditContext.getCurrentUser();

        return AuditLog.builder()
            .entityName(getEntityName(entity))
            .entityId(getEntityId(entity))
            .operation(operation)
            .userId(currentUser != null ? currentUser.getUserId() : null)
            .userNickname(AuditContext.getCurrentUserNickname())
            .reason(AuditContext.getCurrentOperation())
            .build();
    }

    private String getEntityName(Object entity) {
        if (entity instanceof AuditableEntity auditableEntity) {
            return auditableEntity.getEntityName();
        }
        return entity.getClass().getSimpleName();
    }

    private String getEntityId(Object entity) {
        if (entity instanceof AuditableEntity auditableEntity) {
            Object id = auditableEntity.getId();
            return id != null ? id.toString() : "null";
        }

        // Try to find ID field using reflection
        try {
            Field idField = findIdField(entity.getClass());
            if (idField != null) {
                idField.setAccessible(true);
                Object id = idField.get(entity);
                return id != null ? id.toString() : "null";
            }
        } catch (Exception e) {
            log.warn("Failed to extract entity ID: {}", e.getMessage());
        }

        return "unknown";
    }

    private Field findIdField(Class<?> clazz) {
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(jakarta.persistence.Id.class)) {
                return field;
            }
        }

        // Check superclass
        if (clazz.getSuperclass() != null) {
            return findIdField(clazz.getSuperclass());
        }

        return null;
    }

    private Object getOriginalEntity(Object entity) {
        try {
            if (entity instanceof AuditableEntity auditableEntity) {
                Object id = auditableEntity.getId();
                if (id != null) {
                    return entityManager.find(entity.getClass(), id);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to retrieve original entity: {}", e.getMessage());
        }
        return null;
    }

    private List<AuditDetail> extractFieldDetails(Object entity, Object oldEntity, Object newEntity) {
        List<AuditDetail> details = new ArrayList<>();

        Field[] fields = getAllFields(entity.getClass());

        for (Field field : fields) {
            if (shouldExcludeField(field)) {
                continue;
            }

            try {
                field.setAccessible(true);

                Object oldValue = oldEntity != null ? field.get(oldEntity) : null;
                Object newValue = newEntity != null ? field.get(newEntity) : null;

                // Skip if values are the same
                if (Objects.equals(oldValue, newValue)) {
                    continue;
                }

                AuditDetail detail = AuditDetail.builder()
                    .fieldName(field.getName())
                    .fieldType(field.getType().getSimpleName())
                    .oldValue(JsonUtils.toSafeString(oldValue, maxFieldLength))
                    .newValue(JsonUtils.toSafeString(newValue, maxFieldLength))
                    .isSensitive(isSensitiveField(field))
                    .build();

                details.add(detail);

            } catch (Exception e) {
                log.warn("Failed to extract field value for {}: {}", field.getName(), e.getMessage());
            }
        }

        return details;
    }

    private Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    private boolean shouldExcludeField(Field field) {
        String fieldName = field.getName().toLowerCase();

        // Exclude static and transient fields
        if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
            java.lang.reflect.Modifier.isTransient(field.getModifiers())) {
            return true;
        }

        // Exclude JPA annotations
        if (field.isAnnotationPresent(jakarta.persistence.Transient.class)) {
            return true;
        }

        // Exclude configured fields
        return excludedFields.contains(fieldName);
    }

    private boolean isSensitiveField(Field field) {
        String fieldName = field.getName().toLowerCase();
        return fieldName.contains("password") ||
               fieldName.contains("secret") ||
               fieldName.contains("token") ||
               fieldName.contains("key");
    }
}
