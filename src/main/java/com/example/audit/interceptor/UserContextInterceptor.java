package com.example.audit.interceptor;

import com.example.audit.dto.LoginUserInfo;
import com.example.audit.util.AuditContext;
import com.example.audit.util.JsonUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * HTTP interceptor that extracts user information from request headers
 * and sets up the audit context for the current request.
 * 
 * This interceptor looks for the configured user header (default: x-user)
 * and parses the JSON content into a LoginUserInfo object, making it
 * available to the audit system throughout the request lifecycle.
 * 
 * <AUTHOR> Agent
 */
@Component
@Slf4j
public class UserContextInterceptor implements HandlerInterceptor {
    
    @Value("${audit.user-header:x-user}")
    private String userHeader;
    
    /**
     * Called before the handler method is invoked.
     * Extracts user information from headers and sets up audit context.
     * 
     * @param request the HTTP request
     * @param response the HTTP response
     * @param handler the handler method
     * @return true to continue processing
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // Extract user information from header
            String userJson = request.getHeader(userHeader);
            
            if (userJson != null && !userJson.trim().isEmpty()) {
                log.debug("Found user header '{}' with value: {}", userHeader, userJson);
                
                // Parse JSON to LoginUserInfo
                LoginUserInfo userInfo = JsonUtils.fromJson(userJson, LoginUserInfo.class);
                
                if (userInfo != null) {
                    // Set user context for audit
                    AuditContext.setCurrentUser(userInfo);
                    log.debug("Set audit context for user: {} (ID: {})", 
                             userInfo.getNickName(), userInfo.getUserId());
                } else {
                    log.warn("Failed to parse user information from header: {}", userJson);
                }
            } else {
                log.debug("No user header '{}' found in request", userHeader);
                // Create anonymous user context
                LoginUserInfo anonymousUser = LoginUserInfo.builder()
                    .userId(-1L)
                    .nickName("anonymous")
                    .build();
                AuditContext.setCurrentUser(anonymousUser);
            }
            
            // Set additional context information
            String requestUri = request.getRequestURI();
            String method = request.getMethod();
            AuditContext.setOperation(method + " " + requestUri);
            
        } catch (Exception e) {
            log.error("Error setting up audit context: {}", e.getMessage(), e);
            // Continue processing even if audit context setup fails
        }
        
        return true;
    }
    
    /**
     * Called after the handler method completes.
     * Cleans up the audit context to prevent memory leaks.
     * 
     * @param request the HTTP request
     * @param response the HTTP response
     * @param handler the handler method
     * @param ex any exception thrown by the handler
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        try {
            // Clear audit context to prevent memory leaks
            AuditContext.clear();
            log.debug("Cleared audit context for request: {} {}", 
                     request.getMethod(), request.getRequestURI());
        } catch (Exception e) {
            log.error("Error clearing audit context: {}", e.getMessage(), e);
        }
    }
}
