package com.example.audit.repository;

import com.example.audit.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for User entities.
 * 
 * <AUTHOR> Agent
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * Find user by username
     * 
     * @param username the username
     * @return optional user
     */
    Optional<User> findByUsername(String username);
    
    /**
     * Find user by email
     * 
     * @param email the email
     * @return optional user
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Find users by status
     * 
     * @param status the user status
     * @param pageable pagination information
     * @return page of users
     */
    Page<User> findByStatus(User.UserStatus status, Pageable pageable);
    
    /**
     * Find users by department
     * 
     * @param department the department
     * @param pageable pagination information
     * @return page of users
     */
    Page<User> findByDepartment(String department, Pageable pageable);
    
    /**
     * Search users by name or email
     * 
     * @param searchTerm the search term
     * @param pageable pagination information
     * @return page of users
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<User> searchUsers(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Check if username exists
     * 
     * @param username the username
     * @return true if exists
     */
    boolean existsByUsername(String username);
    
    /**
     * Check if email exists
     * 
     * @param email the email
     * @return true if exists
     */
    boolean existsByEmail(String email);
}
