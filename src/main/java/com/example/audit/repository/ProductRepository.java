package com.example.audit.repository;

import com.example.audit.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Product entities.
 * 
 * <AUTHOR> Agent
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    /**
     * Find product by SKU
     * 
     * @param sku the SKU
     * @return optional product
     */
    Optional<Product> findBySku(String sku);
    
    /**
     * Find products by category
     * 
     * @param category the category
     * @param pageable pagination information
     * @return page of products
     */
    Page<Product> findByCategory(String category, Pageable pageable);
    
    /**
     * Find products by status
     * 
     * @param status the product status
     * @param pageable pagination information
     * @return page of products
     */
    Page<Product> findByStatus(Product.ProductStatus status, Pageable pageable);
    
    /**
     * Find products by brand
     * 
     * @param brand the brand
     * @param pageable pagination information
     * @return page of products
     */
    Page<Product> findByBrand(String brand, Pageable pageable);
    
    /**
     * Find products with low stock
     * 
     * @param pageable pagination information
     * @return page of products
     */
    @Query("SELECT p FROM Product p WHERE p.stockQuantity <= p.minStockLevel")
    Page<Product> findLowStockProducts(Pageable pageable);
    
    /**
     * Find products by price range
     * 
     * @param minPrice minimum price
     * @param maxPrice maximum price
     * @param pageable pagination information
     * @return page of products
     */
    Page<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    /**
     * Search products by name, SKU, or description
     * 
     * @param searchTerm the search term
     * @param pageable pagination information
     * @return page of products
     */
    @Query("SELECT p FROM Product p WHERE " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.sku) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Product> searchProducts(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Get distinct categories
     * 
     * @return list of categories
     */
    @Query("SELECT DISTINCT p.category FROM Product p ORDER BY p.category")
    List<String> findDistinctCategories();
    
    /**
     * Get distinct brands
     * 
     * @return list of brands
     */
    @Query("SELECT DISTINCT p.brand FROM Product p WHERE p.brand IS NOT NULL ORDER BY p.brand")
    List<String> findDistinctBrands();
    
    /**
     * Check if SKU exists
     * 
     * @param sku the SKU
     * @return true if exists
     */
    boolean existsBySku(String sku);
}
