package com.example.audit.listener;

import com.example.audit.entity.AuditableEntity;
import com.example.audit.service.AuditService;
import com.example.audit.util.AuditContext;
import jakarta.persistence.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * JPA Entity Listener that captures database operations and triggers audit logging.
 * 
 * This listener is automatically invoked by JPA/Hibernate when entities are
 * persisted, updated, or removed. It integrates with the AuditService to
 * create detailed audit records with field-level change tracking.
 * 
 * <AUTHOR> Agent
 */
@Component
@Slf4j
public class AuditEntityListener {
    
    private static AuditService auditService;
    
    /**
     * Inject the audit service using setter injection to work around
     * JPA entity listener instantiation limitations
     */
    @Autowired
    public void setAuditService(AuditService auditService) {
        AuditEntityListener.auditService = auditService;
    }
    
    /**
     * Called before an entity is persisted to the database
     * 
     * @param entity the entity being persisted
     */
    @PrePersist
    public void prePersist(Object entity) {
        log.debug("Pre-persist audit for entity: {}", entity.getClass().getSimpleName());
        
        if (entity instanceof AuditableEntity auditableEntity) {
            // Set audit fields
            String currentUser = AuditContext.getCurrentUserNickname();
            auditableEntity.setCreatedBy(currentUser);
            auditableEntity.setUpdatedBy(currentUser);
        }
    }
    
    /**
     * Called after an entity is persisted to the database
     * 
     * @param entity the entity that was persisted
     */
    @PostPersist
    public void postPersist(Object entity) {
        log.debug("Post-persist audit for entity: {}", entity.getClass().getSimpleName());
        
        if (auditService != null) {
            try {
                auditService.auditCreate(entity);
            } catch (Exception e) {
                log.error("Failed to audit entity creation: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * Called before an entity is updated in the database
     * 
     * @param entity the entity being updated
     */
    @PreUpdate
    public void preUpdate(Object entity) {
        log.debug("Pre-update audit for entity: {}", entity.getClass().getSimpleName());
        
        if (entity instanceof AuditableEntity auditableEntity) {
            // Set audit fields
            String currentUser = AuditContext.getCurrentUserNickname();
            auditableEntity.setUpdatedBy(currentUser);
        }
    }
    
    /**
     * Called after an entity is updated in the database
     * 
     * @param entity the entity that was updated
     */
    @PostUpdate
    public void postUpdate(Object entity) {
        log.debug("Post-update audit for entity: {}", entity.getClass().getSimpleName());
        
        if (auditService != null) {
            try {
                auditService.auditUpdate(entity);
            } catch (Exception e) {
                log.error("Failed to audit entity update: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * Called before an entity is removed from the database
     * 
     * @param entity the entity being removed
     */
    @PreRemove
    public void preRemove(Object entity) {
        log.debug("Pre-remove audit for entity: {}", entity.getClass().getSimpleName());
    }
    
    /**
     * Called after an entity is removed from the database
     * 
     * @param entity the entity that was removed
     */
    @PostRemove
    public void postRemove(Object entity) {
        log.debug("Post-remove audit for entity: {}", entity.getClass().getSimpleName());
        
        if (auditService != null) {
            try {
                auditService.auditDelete(entity);
            } catch (Exception e) {
                log.error("Failed to audit entity deletion: {}", e.getMessage(), e);
            }
        }
    }
}
