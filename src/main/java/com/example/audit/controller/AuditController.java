package com.example.audit.controller;

import com.example.audit.entity.AuditLog;
import com.example.audit.repository.AuditLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for audit trail operations.
 * Provides endpoints for querying audit logs with various filters.
 * 
 * <AUTHOR> Agent
 */
@RestController
@RequestMapping("/api/audit")
@RequiredArgsConstructor
@Slf4j
public class AuditController {
    
    private final AuditLogRepository auditLogRepository;
    
    /**
     * Get audit log by ID
     * 
     * @param id the audit log ID
     * @return the audit log with details
     */
    @GetMapping("/{id}")
    public ResponseEntity<AuditLog> getAuditLogById(@PathVariable Long id) {
        log.debug("Getting audit log by ID: {}", id);
        
        Optional<AuditLog> auditLog = auditLogRepository.findById(id);
        return auditLog.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Get audit trail for a specific entity
     * 
     * @param entityName the entity class name
     * @param entityId the entity ID
     * @param page page number (0-based)
     * @param size page size
     * @return page of audit logs
     */
    @GetMapping("/entity/{entityName}/{entityId}")
    public ResponseEntity<Page<AuditLog>> getEntityAuditTrail(
            @PathVariable String entityName,
            @PathVariable String entityId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Getting audit trail for entity: {} with ID: {}", entityName, entityId);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "timestamp"));
        Page<AuditLog> auditLogs = auditLogRepository.findByEntityNameAndEntityIdOrderByTimestampDesc(
            entityName, entityId, pageable);
        
        return ResponseEntity.ok(auditLogs);
    }
    
    /**
     * Get audit logs with filters
     * 
     * @param entityName entity name filter (optional)
     * @param entityId entity ID filter (optional)
     * @param userId user ID filter (optional)
     * @param operation operation filter (optional)
     * @param startDate start date filter (optional)
     * @param endDate end date filter (optional)
     * @param page page number (0-based)
     * @param size page size
     * @return page of audit logs
     */
    @GetMapping
    public ResponseEntity<Page<AuditLog>> getAuditLogs(
            @RequestParam(required = false) String entityName,
            @RequestParam(required = false) String entityId,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) AuditLog.AuditOperation operation,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Getting audit logs with filters - entityName: {}, entityId: {}, userId: {}, operation: {}", 
                 entityName, entityId, userId, operation);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "timestamp"));
        Page<AuditLog> auditLogs = auditLogRepository.findWithFilters(
            entityName, entityId, userId, operation, startDate, endDate, pageable);
        
        return ResponseEntity.ok(auditLogs);
    }
    
    /**
     * Get recent audit logs for dashboard
     * 
     * @param limit maximum number of records
     * @return list of recent audit logs
     */
    @GetMapping("/recent")
    public ResponseEntity<List<AuditLog>> getRecentAuditLogs(
            @RequestParam(defaultValue = "50") int limit) {
        
        log.debug("Getting recent audit logs with limit: {}", limit);
        
        Pageable pageable = PageRequest.of(0, limit);
        List<AuditLog> auditLogs = auditLogRepository.findRecentAuditLogs(pageable);
        
        return ResponseEntity.ok(auditLogs);
    }
    
    /**
     * Get audit logs by user
     * 
     * @param userId the user ID
     * @param page page number (0-based)
     * @param size page size
     * @return page of audit logs
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Page<AuditLog>> getAuditLogsByUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Getting audit logs for user ID: {}", userId);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "timestamp"));
        Page<AuditLog> auditLogs = auditLogRepository.findByUserIdOrderByTimestampDesc(userId, pageable);
        
        return ResponseEntity.ok(auditLogs);
    }
    
    /**
     * Get audit logs by operation type
     * 
     * @param operation the operation type
     * @param page page number (0-based)
     * @param size page size
     * @return page of audit logs
     */
    @GetMapping("/operation/{operation}")
    public ResponseEntity<Page<AuditLog>> getAuditLogsByOperation(
            @PathVariable AuditLog.AuditOperation operation,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Getting audit logs for operation: {}", operation);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "timestamp"));
        Page<AuditLog> auditLogs = auditLogRepository.findByOperationOrderByTimestampDesc(operation, pageable);
        
        return ResponseEntity.ok(auditLogs);
    }
    
    /**
     * Get audit count for a specific entity
     * 
     * @param entityName the entity class name
     * @param entityId the entity ID
     * @return count of audit logs
     */
    @GetMapping("/count/{entityName}/{entityId}")
    public ResponseEntity<Long> getEntityAuditCount(
            @PathVariable String entityName,
            @PathVariable String entityId) {
        
        log.debug("Getting audit count for entity: {} with ID: {}", entityName, entityId);
        
        long count = auditLogRepository.countByEntityNameAndEntityId(entityName, entityId);
        return ResponseEntity.ok(count);
    }
}
