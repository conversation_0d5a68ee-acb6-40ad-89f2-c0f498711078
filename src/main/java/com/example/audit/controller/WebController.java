package com.example.audit.controller;

import com.example.audit.entity.User;
import com.example.audit.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Web controller for serving HTML pages with audit functionality.
 * 
 * <AUTHOR> Agent
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class WebController {
    
    private final UserService userService;
    
    /**
     * Home page
     * 
     * @param model the model
     * @return the home template
     */
    @GetMapping("/")
    public String home(Model model) {
        log.debug("Serving home page");
        
        // Get recent users for display
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<User> recentUsers = userService.getAllUsers(pageable);
        
        model.addAttribute("recentUsers", recentUsers.getContent());
        model.addAttribute("totalUsers", recentUsers.getTotalElements());
        
        return "index";
    }
    
    /**
     * Users management page
     * 
     * @param page page number
     * @param size page size
     * @param search search term
     * @param model the model
     * @return the users template
     */
    @GetMapping("/users")
    public String users(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            Model model) {
        
        log.debug("Serving users page - page: {}, size: {}, search: {}", page, size, search);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("firstName", "lastName"));
        Page<User> users;
        
        if (search != null && !search.trim().isEmpty()) {
            users = userService.searchUsers(search, pageable);
            model.addAttribute("search", search);
        } else {
            users = userService.getAllUsers(pageable);
        }
        
        model.addAttribute("users", users);
        model.addAttribute("currentPage", page);
        model.addAttribute("pageSize", size);
        
        return "users";
    }
    
    /**
     * User detail page
     * 
     * @param id the user ID
     * @param model the model
     * @return the user detail template
     */
    @GetMapping("/users/{id}")
    public String userDetail(@PathVariable Long id, Model model) {
        log.debug("Serving user detail page for ID: {}", id);
        
        return userService.getUserById(id)
            .map(user -> {
                model.addAttribute("user", user);
                return "user-detail";
            })
            .orElse("redirect:/users?error=User not found");
    }
    
    /**
     * Audit trail page for a specific entity
     * 
     * @param entityName the entity name
     * @param entityId the entity ID
     * @param model the model
     * @return the audit trail template
     */
    @GetMapping("/audit/{entityName}/{entityId}")
    public String auditTrail(
            @PathVariable String entityName,
            @PathVariable String entityId,
            Model model) {
        
        log.debug("Serving audit trail page for entity: {} with ID: {}", entityName, entityId);
        
        model.addAttribute("entityName", entityName);
        model.addAttribute("entityId", entityId);
        
        // Add entity details if it's a User
        if ("User".equals(entityName)) {
            try {
                Long userId = Long.parseLong(entityId);
                userService.getUserById(userId).ifPresent(user -> 
                    model.addAttribute("entity", user));
            } catch (NumberFormatException e) {
                log.warn("Invalid user ID format: {}", entityId);
            }
        }
        
        return "audit-trail";
    }
    
    /**
     * General audit dashboard page
     * 
     * @param model the model
     * @return the audit dashboard template
     */
    @GetMapping("/audit")
    public String auditDashboard(Model model) {
        log.debug("Serving audit dashboard page");
        
        return "audit-dashboard";
    }
}
