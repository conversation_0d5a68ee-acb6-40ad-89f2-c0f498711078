package com.example.audit.config;

import com.example.audit.dto.LoginUserInfo;
import com.example.audit.entity.User;
import com.example.audit.service.UserService;
import com.example.audit.util.AuditContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Data initializer to create sample data for demonstration purposes.
 * This component runs after the application starts and creates sample users
 * to demonstrate the audit functionality.
 * 
 * <AUTHOR> Agent
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {
    
    private final UserService userService;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing sample data for audit demo...");
        
        try {
            // Set up system user context for data initialization
            LoginUserInfo systemUser = LoginUserInfo.builder()
                .userId(0L)
                .nickName("system")
                .fromType("initialization")
                .build();
            
            AuditContext.setCurrentUser(systemUser);
            
            // Create sample users if none exist
            if (userService.getAllUsers(org.springframework.data.domain.PageRequest.of(0, 1)).isEmpty()) {
                createSampleUsers();
            } else {
                log.info("Sample data already exists, skipping initialization");
            }
            
        } catch (Exception e) {
            log.error("Failed to initialize sample data: {}", e.getMessage(), e);
        } finally {
            // Clear audit context
            AuditContext.clear();
        }
    }
    
    private void createSampleUsers() {
        log.info("Creating sample users...");
        
        // Create admin user
        User admin = User.builder()
            .username("admin")
            .email("<EMAIL>")
            .firstName("System")
            .lastName("Administrator")
            .department("IT")
            .jobTitle("System Administrator")
            .phoneNumber("******-0001")
            .status(User.UserStatus.ACTIVE)
            .notes("System administrator account")
            .build();
        
        userService.createUser(admin);
        log.info("Created admin user");
        
        // Create sample employees
        User[] sampleUsers = {
            User.builder()
                .username("john.doe")
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .department("Engineering")
                .jobTitle("Senior Software Engineer")
                .phoneNumber("******-0101")
                .status(User.UserStatus.ACTIVE)
                .notes("Lead developer for the audit system")
                .build(),
                
            User.builder()
                .username("jane.smith")
                .email("<EMAIL>")
                .firstName("Jane")
                .lastName("Smith")
                .department("Product Management")
                .jobTitle("Product Manager")
                .phoneNumber("******-0102")
                .status(User.UserStatus.ACTIVE)
                .notes("Product manager for enterprise features")
                .build(),
                
            User.builder()
                .username("mike.wilson")
                .email("<EMAIL>")
                .firstName("Mike")
                .lastName("Wilson")
                .department("QA")
                .jobTitle("QA Engineer")
                .phoneNumber("******-0103")
                .status(User.UserStatus.ACTIVE)
                .notes("Quality assurance specialist")
                .build(),
                
            User.builder()
                .username("sarah.johnson")
                .email("<EMAIL>")
                .firstName("Sarah")
                .lastName("Johnson")
                .department("DevOps")
                .jobTitle("DevOps Engineer")
                .phoneNumber("******-0104")
                .status(User.UserStatus.ACTIVE)
                .notes("Infrastructure and deployment specialist")
                .build(),
                
            User.builder()
                .username("david.brown")
                .email("<EMAIL>")
                .firstName("David")
                .lastName("Brown")
                .department("Security")
                .jobTitle("Security Analyst")
                .phoneNumber("******-0105")
                .status(User.UserStatus.INACTIVE)
                .notes("Security compliance and audit specialist")
                .build()
        };
        
        for (User user : sampleUsers) {
            userService.createUser(user);
            log.info("Created user: {}", user.getUsername());
        }
        
        log.info("Sample data initialization completed. Created {} users", sampleUsers.length + 1);
        
        // Simulate some updates to generate audit trail
        simulateUserUpdates();
    }
    
    private void simulateUserUpdates() {
        log.info("Simulating user updates to generate audit trail...");
        
        try {
            // Simulate different users making changes
            LoginUserInfo[] simulatedUsers = {
                LoginUserInfo.builder()
                    .userId(1L)
                    .nickName("admin")
                    .fromType("web")
                    .build(),
                LoginUserInfo.builder()
                    .userId(2L)
                    .nickName("hr_manager")
                    .fromType("web")
                    .build(),
                LoginUserInfo.builder()
                    .userId(3L)
                    .nickName("department_head")
                    .fromType("api")
                    .build()
            };
            
            // Find John Doe and update his information
            userService.getUserByUsername("john.doe").ifPresent(user -> {
                AuditContext.setCurrentUser(simulatedUsers[0]); // admin
                user.setJobTitle("Lead Software Engineer");
                user.setDepartment("Engineering - Backend");
                userService.updateUser(user.getId(), user);
                log.info("Updated John Doe's job title and department");
            });
            
            // Find Jane Smith and update her phone
            userService.getUserByUsername("jane.smith").ifPresent(user -> {
                AuditContext.setCurrentUser(simulatedUsers[1]); // hr_manager
                user.setPhoneNumber("******-0202");
                user.setNotes("Product manager for enterprise features - Updated contact info");
                userService.updateUser(user.getId(), user);
                log.info("Updated Jane Smith's phone number");
            });
            
            // Find David Brown and change his status
            userService.getUserByUsername("david.brown").ifPresent(user -> {
                AuditContext.setCurrentUser(simulatedUsers[2]); // department_head
                user.setStatus(User.UserStatus.ACTIVE);
                user.setNotes("Security compliance and audit specialist - Reactivated");
                userService.updateUser(user.getId(), user);
                log.info("Reactivated David Brown's account");
            });
            
        } catch (Exception e) {
            log.error("Failed to simulate user updates: {}", e.getMessage(), e);
        } finally {
            AuditContext.clear();
        }
        
        log.info("User update simulation completed");
    }
}
