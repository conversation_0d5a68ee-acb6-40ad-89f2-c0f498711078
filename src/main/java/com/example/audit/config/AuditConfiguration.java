package com.example.audit.config;

import com.example.audit.util.AuditContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import java.util.Optional;

/**
 * Configuration class for JPA auditing.
 * Sets up automatic auditing of entity creation and modification.
 * 
 * <AUTHOR> Agent
 */
@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class AuditConfiguration {
    
    /**
     * Provides the current auditor for JPA auditing.
     * Uses the user context from AuditContext.
     * 
     * @return AuditorAware implementation
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return new AuditorAwareImpl();
    }
    
    /**
     * Implementation of AuditorAware that retrieves the current user
     * from the audit context.
     */
    public static class AuditorAwareImpl implements AuditorAware<String> {
        
        @Override
        public Optional<String> getCurrentAuditor() {
            String currentUser = AuditContext.getCurrentUserNickname();
            return Optional.ofNullable(currentUser);
        }
    }
}
