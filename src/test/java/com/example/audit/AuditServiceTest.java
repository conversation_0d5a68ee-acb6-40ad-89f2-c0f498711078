package com.example.audit;

import com.example.audit.dto.LoginUserInfo;
import com.example.audit.entity.AuditLog;
import com.example.audit.entity.User;
import com.example.audit.repository.AuditLogRepository;
import com.example.audit.repository.UserRepository;
import com.example.audit.service.UserService;
import com.example.audit.util.AuditContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for the audit functionality.
 * 
 * <AUTHOR> Agent
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class AuditServiceTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AuditLogRepository auditLogRepository;
    
    @BeforeEach
    void setUp() {
        // Set up audit context for tests
        LoginUserInfo testUser = LoginUserInfo.builder()
            .userId(999L)
            .nickName("test_user")
            .fromType("test")
            .build();
        
        AuditContext.setCurrentUser(testUser);
    }
    
    @Test
    void testUserCreationAudit() {
        // Given
        User user = User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .firstName("Test")
            .lastName("User")
            .department("IT")
            .status(User.UserStatus.ACTIVE)
            .build();
        
        // When
        User savedUser = userService.createUser(user);
        
        // Then
        assertThat(savedUser.getId()).isNotNull();
        assertThat(savedUser.getCreatedBy()).isEqualTo("test_user");
        
        // Check audit log
        Page<AuditLog> auditLogs = auditLogRepository.findByEntityNameAndEntityIdOrderByTimestampDesc(
            "User", savedUser.getId().toString(), PageRequest.of(0, 10));
        
        assertThat(auditLogs.getContent()).hasSize(1);
        
        AuditLog auditLog = auditLogs.getContent().get(0);
        assertThat(auditLog.getOperation()).isEqualTo(AuditLog.AuditOperation.CREATE);
        assertThat(auditLog.getUserNickname()).isEqualTo("test_user");
        assertThat(auditLog.getUserId()).isEqualTo(999L);
        assertThat(auditLog.getDetails()).isNotEmpty();
    }
    
    @Test
    void testUserUpdateAudit() {
        // Given - create a user first
        User user = User.builder()
            .username("updatetest")
            .email("<EMAIL>")
            .firstName("Update")
            .lastName("Test")
            .department("HR")
            .status(User.UserStatus.ACTIVE)
            .build();
        
        User savedUser = userService.createUser(user);
        
        // When - update the user
        savedUser.setDepartment("IT");
        savedUser.setJobTitle("Developer");
        User updatedUser = userService.updateUser(savedUser.getId(), savedUser);
        
        // Then
        assertThat(updatedUser.getDepartment()).isEqualTo("IT");
        assertThat(updatedUser.getJobTitle()).isEqualTo("Developer");
        assertThat(updatedUser.getUpdatedBy()).isEqualTo("test_user");
        
        // Check audit logs
        Page<AuditLog> auditLogs = auditLogRepository.findByEntityNameAndEntityIdOrderByTimestampDesc(
            "User", savedUser.getId().toString(), PageRequest.of(0, 10));
        
        assertThat(auditLogs.getContent()).hasSize(2); // CREATE + UPDATE
        
        AuditLog updateAuditLog = auditLogs.getContent().get(0); // Most recent
        assertThat(updateAuditLog.getOperation()).isEqualTo(AuditLog.AuditOperation.UPDATE);
        assertThat(updateAuditLog.getUserNickname()).isEqualTo("test_user");
        
        // Check that field changes are captured
        assertThat(updateAuditLog.getDetails()).isNotEmpty();
        
        boolean departmentChangeFound = updateAuditLog.getDetails().stream()
            .anyMatch(detail -> "department".equals(detail.getFieldName()) &&
                               "HR".equals(detail.getOldValue()) &&
                               "IT".equals(detail.getNewValue()));
        
        assertThat(departmentChangeFound).isTrue();
    }
    
    @Test
    void testUserDeletionAudit() {
        // Given - create a user first
        User user = User.builder()
            .username("deletetest")
            .email("<EMAIL>")
            .firstName("Delete")
            .lastName("Test")
            .status(User.UserStatus.ACTIVE)
            .build();
        
        User savedUser = userService.createUser(user);
        
        // When - delete the user
        userService.deleteUser(savedUser.getId());
        
        // Then
        // Check audit logs
        Page<AuditLog> auditLogs = auditLogRepository.findByEntityNameAndEntityIdOrderByTimestampDesc(
            "User", savedUser.getId().toString(), PageRequest.of(0, 10));
        
        assertThat(auditLogs.getContent()).hasSize(2); // CREATE + UPDATE (soft delete)
        
        AuditLog updateAuditLog = auditLogs.getContent().get(0); // Most recent
        assertThat(updateAuditLog.getOperation()).isEqualTo(AuditLog.AuditOperation.UPDATE);
        assertThat(updateAuditLog.getUserNickname()).isEqualTo("test_user");
        
        // Check that deleted flag change is captured
        boolean deletedChangeFound = updateAuditLog.getDetails().stream()
            .anyMatch(detail -> "deleted".equals(detail.getFieldName()) &&
                               "false".equals(detail.getOldValue()) &&
                               "true".equals(detail.getNewValue()));
        
        assertThat(deletedChangeFound).isTrue();
    }
    
    @Test
    void testAuditLogFiltering() {
        // Given - create multiple users
        User user1 = userService.createUser(User.builder()
            .username("filter1")
            .email("<EMAIL>")
            .firstName("Filter")
            .lastName("One")
            .build());
        
        User user2 = userService.createUser(User.builder()
            .username("filter2")
            .email("<EMAIL>")
            .firstName("Filter")
            .lastName("Two")
            .build());
        
        // When - query audit logs
        Page<AuditLog> allAuditLogs = auditLogRepository.findAll(PageRequest.of(0, 10));
        Page<AuditLog> user1AuditLogs = auditLogRepository.findByEntityNameAndEntityIdOrderByTimestampDesc(
            "User", user1.getId().toString(), PageRequest.of(0, 10));
        Page<AuditLog> createAuditLogs = auditLogRepository.findByOperationOrderByTimestampDesc(
            AuditLog.AuditOperation.CREATE, PageRequest.of(0, 10));
        
        // Then
        assertThat(allAuditLogs.getContent().size()).isGreaterThanOrEqualTo(2);
        assertThat(user1AuditLogs.getContent()).hasSize(1);
        assertThat(createAuditLogs.getContent().size()).isGreaterThanOrEqualTo(2);
        
        // Verify user-specific filtering
        assertThat(user1AuditLogs.getContent().get(0).getEntityId())
            .isEqualTo(user1.getId().toString());
    }
}
