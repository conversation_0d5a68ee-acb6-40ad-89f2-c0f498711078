# JPA Auditing Demo

A comprehensive, reusable data auditing system demonstration using JDK 17, <PERSON>ven, Spring Boot 3.2.x, JPA, and MySQL. This system provides field-level change tracking for database entities with enterprise-grade features suitable for deployment across 80+ Spring Boot microservices.

## 🚀 Features

### Core Functionality
- **Field-Level Change Tracking**: Capture granular changes (old value → new value) for all database operations (CREATE, UPDATE, DELETE)
- **User Context Management**: Extract and record user information from the `x-user` HTTP header
- **Temporal Auditing**: Record precise timestamps with timezone support
- **Multi-Entity Support**: Generic auditing framework for different entity types

### Technical Architecture
- **Framework**: Spring Boot 3.2.x with Spring Data JPA/Hibernate
- **Database**: MySQL 8.0+ for audit data persistence
- **User Context**: HTTP interceptor for `x-user` header parsing
- **Change Detection**: JPA entity listeners for automatic change detection
- **Generic Design**: Abstract base classes and annotations for easy adoption

### User Interface
- **Audit Trail UI**: "Operation History" button on entity detail pages
- **Detailed Change View**: Comprehensive audit information display
- **Filtering**: Filter by user, operation type, date range
- **Timeline View**: Visual timeline of changes

## 📋 Prerequisites

- JDK 17 or higher
- Maven 3.6+
- MySQL 8.0+ (or use H2 for testing)
- IDE with Spring Boot support (IntelliJ IDEA, Eclipse, VS Code)

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd jpa-auditing-demo
```

### 2. Database Configuration

Update `src/main/resources/application.yml` with your MySQL connection details:

```yaml
spring:
  datasource:
    url: ********************************************************************
    username: your_username
    password: your_password
```

### 3. Run the Application

```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080/audit-demo`

### 4. Test the Audit System

1. **Create a User** via API:
```bash
curl -X POST http://localhost:8080/audit-demo/api/users \
  -H "Content-Type: application/json" \
  -H "x-user: {\"userId\":1,\"nickName\":\"admin\",\"fromType\":\"api\"}" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "department": "IT",
    "status": "ACTIVE"
  }'
```

2. **Update the User**:
```bash
curl -X PUT http://localhost:8080/audit-demo/api/users/1 \
  -H "Content-Type: application/json" \
  -H "x-user: {\"userId\":2,\"nickName\":\"manager\",\"fromType\":\"api\"}" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "department": "Engineering",
    "jobTitle": "Senior Developer",
    "status": "ACTIVE"
  }'
```

3. **View Audit Trail**:
```bash
curl http://localhost:8080/audit-demo/api/audit/entity/User/1
```

## 🏗️ Architecture Overview

### Core Components

#### 1. Audit Entities
- **`AuditLog`**: Main audit record with operation metadata
- **`AuditDetail`**: Field-level change details
- **`AuditableEntity`**: Abstract base class for auditable entities

#### 2. Audit Infrastructure
- **`AuditContext`**: Thread-local user context management
- **`AuditEntityListener`**: JPA entity listener for change detection
- **`UserContextInterceptor`**: HTTP interceptor for user header parsing
- **`AuditService`**: Core audit processing service

#### 3. Sample Entities
- **`User`**: Sample user entity with audit capabilities
- **`Product`**: Sample product entity with audit capabilities

### Data Flow

1. **Request Processing**: HTTP interceptor extracts user info from `x-user` header
2. **Context Setup**: User context stored in thread-local storage
3. **Entity Operations**: JPA operations trigger entity listeners
4. **Change Detection**: Listeners compare old vs new entity states
5. **Audit Recording**: Detailed audit records saved asynchronously
6. **Context Cleanup**: Thread-local context cleared after request

## 🔧 Configuration

### Application Properties

```yaml
# Audit Configuration
audit:
  enabled: true                    # Enable/disable auditing
  async: true                     # Asynchronous audit processing
  retention-days: 365             # Audit data retention period
  max-field-length: 1000          # Maximum field value length
  excluded-fields:                # Fields to exclude from auditing
    - password
    - token
    - secret
  user-header: x-user             # HTTP header for user context
```

### User Context Header Format

The `x-user` header should contain JSON with user information:

```json
{
  "userId": 123,
  "nickName": "john.doe",
  "fromType": "web",
  "cId": "company123",
  "roles": "admin,user",
  "companyIds": "1,2,3"
}
```

## 📊 API Endpoints

### User Management
- `GET /api/users` - List users with pagination
- `POST /api/users` - Create new user
- `GET /api/users/{id}` - Get user by ID
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user (soft delete)
- `GET /api/users/search?q={term}` - Search users

### Audit Trail
- `GET /api/audit/entity/{entityName}/{entityId}` - Get entity audit trail
- `GET /api/audit?filters...` - Get audit logs with filters
- `GET /api/audit/recent?limit={n}` - Get recent audit logs
- `GET /api/audit/user/{userId}` - Get user's audit logs
- `GET /api/audit/operation/{operation}` - Get logs by operation type

### Web Interface
- `/` - Home dashboard
- `/users` - User management interface
- `/users/{id}` - User detail page
- `/audit/{entityName}/{entityId}` - Audit trail viewer
- `/audit` - Audit dashboard

## 🧪 Testing

### Run Tests
```bash
mvn test
```

### Test Coverage
- Unit tests for audit service functionality
- Integration tests for end-to-end audit flow
- API tests for REST endpoints
- UI tests for web interface

### Sample Test Scenarios
1. **User Creation Audit**: Verify CREATE operation tracking
2. **User Update Audit**: Verify field-level change detection
3. **User Deletion Audit**: Verify soft delete tracking
4. **Audit Filtering**: Verify query filtering capabilities

## 🚀 Enterprise Adoption

### Integration Steps

1. **Add Dependency**: Include the audit starter in your `pom.xml`
2. **Extend Base Class**: Make your entities extend `AuditableEntity`
3. **Configure Properties**: Set audit configuration in `application.yml`
4. **Add Interceptor**: Register `UserContextInterceptor` in your web config
5. **Test Integration**: Verify audit functionality with your entities

### Example Entity Integration

```java
@Entity
@Table(name = "orders")
public class Order extends AuditableEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "order_number")
    private String orderNumber;
    
    @Column(name = "total_amount")
    private BigDecimal totalAmount;
    
    // ... other fields and methods
}
```

### Performance Considerations

- **Asynchronous Processing**: Audit operations run asynchronously by default
- **Batch Processing**: Consider batch audit record insertion for high-volume systems
- **Index Optimization**: Ensure proper indexing on audit tables
- **Data Retention**: Implement audit data archival for long-term storage

## 📈 Monitoring and Maintenance

### Health Checks
- Application health endpoint: `/actuator/health`
- Audit system status monitoring
- Database connection health

### Data Retention
- Configurable retention period
- Automated cleanup of old audit records
- Archive strategy for compliance requirements

### Performance Monitoring
- Audit operation metrics
- Database query performance
- Memory usage monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions and support:
- Create an issue in the repository
- Check the documentation in the `/docs` folder
- Review the example implementations in the test classes

---

**Built with ❤️ for enterprise-grade audit tracking**
